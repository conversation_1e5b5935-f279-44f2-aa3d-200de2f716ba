"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import { SellzioFacet } from '@/components/themes/sellzio/sellzio-facet'
import SellzioCategories from '@/components/themes/sellzio/categories'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'
// Import data dari file terpisah
import { sampleProducts, type Product as ProductType } from '@/components/data/products'
import { keywordPredictionDB } from '@/components/data/keywords'

// Type definitions
interface Prediction {
  text: string
  type: string
  relevance: number
}

interface Product {
  id: number
  name: string
  shortName: string
  category: string
  price: string
  originalPrice: string
  discount: string
  rating: string
  sold: string
  shipping: string
  image: string
  isMall: boolean
  cod: boolean
  searchScore?: number
  matchDetails?: string[]
}

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [originalSearchResults, setOriginalSearchResults] = useState<Product[]>([]) // Store original results for filtering
  const [isSearchResultShown, setIsSearchResultShown] = useState(false)
  const [hideMainContent, setHideMainContent] = useState(false)
  const [userInteractionHistory, setUserInteractionHistory] = useState<string[]>([])
  const [searchFrequency, setSearchFrequency] = useState<{ [key: string]: number }>({})
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [activeFilterTab, setActiveFilterTab] = useState('terkait')
  const [priceSortDirection, setPriceSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showFilterIcon, setShowFilterIcon] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showFacetPanel, setShowFacetPanel] = useState(false)
  const [facetFilters, setFacetFilters] = useState<{[key: string]: string[]}>({})
  const [filterBadgeCount, setFilterBadgeCount] = useState(0)
  const [subcategoryContext, setSubcategoryContext] = useState<{
    category: string
    selectedSubcategory: string
    allSubcategories: any[]
  } | null>(null)

  // State untuk suggestions popup
  const [showSuggestionsPopup, setShowSuggestionsPopup] = useState(false)

  // State untuk successful search history - riwayat pencarian yang berhasil menemukan produk
  const [successfulSearchHistory, setSuccessfulSearchHistory] = useState<string[]>([])

  // Function to load user interaction history from localStorage
  const loadUserInteractionHistory = () => {
    try {
      const history = localStorage.getItem('keywordPredictionHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setUserInteractionHistory(parsedHistory)
        console.log('Loaded prediction history:', parsedHistory)
      } else {
        console.log('No prediction history found in localStorage')
      }
    } catch (e) {
      console.log('Failed to load prediction history from localStorage', e)
    }
  }

  // Function to load search frequency from localStorage
  const loadSearchFrequency = () => {
    try {
      const savedFrequency = localStorage.getItem('searchFrequency')
      if (savedFrequency) {
        setSearchFrequency(JSON.parse(savedFrequency))
      }
    } catch (e) {
      console.log('Failed to load search frequency from localStorage', e)
    }
  }

  // Function to update search frequency
  const updateSearchFrequency = (keyword: string) => {
    setSearchFrequency(prev => {
      const updated = { ...prev }
      updated[keyword] = (updated[keyword] || 0) + 1
      localStorage.setItem('searchFrequency', JSON.stringify(updated))
      return updated
    })
  }

  // Function to save successful search to history - hanya pencarian yang menemukan produk
  const saveSuccessfulSearch = (keyword: string) => {
    if (!keyword || keyword.trim() === '') return

    const normalizedKeyword = keyword.trim().toLowerCase()

    setSuccessfulSearchHistory(prev => {
      // Hapus keyword jika sudah ada (untuk memindahkan ke posisi teratas)
      const filtered = prev.filter(item => item.toLowerCase() !== normalizedKeyword)

      // Tambahkan ke posisi teratas
      const updated = [keyword.trim(), ...filtered].slice(0, 20) // Maksimal 20 item

      // Simpan ke localStorage
      localStorage.setItem('successfulSearchHistory', JSON.stringify(updated))

      return updated
    })
  }

  // Function to load successful search history from localStorage
  const loadSuccessfulSearchHistory = () => {
    try {
      const history = localStorage.getItem('successfulSearchHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setSuccessfulSearchHistory(parsedHistory)
        console.log('Loaded successful search history:', parsedHistory)
      }
    } catch (e) {
      console.log('Failed to load successful search history from localStorage', e)
    }
  }

  // Function to convert text to Title Case (huruf besar di depan setiap kata)
  const toTitleCase = (text: string) => {
    return text.toLowerCase()
  }

  // Function to get trending keywords based on search frequency
  const getTrendingKeywords = () => {
    // Default trending keywords if no search frequency data
    const defaultTrending = [
      'tas sekolah', 'tas selempang', 'handphone', 'tas mata', 'tas'
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultTrending
    }

    // Sort keywords by frequency and get top 5
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([keyword]) => keyword)

    // If we have less than 5, fill with defaults
    while (sortedKeywords.length < 5) {
      const nextDefault = defaultTrending[sortedKeywords.length]
      if (nextDefault && !sortedKeywords.includes(nextDefault)) {
        sortedKeywords.push(nextDefault)
      } else {
        break
      }
    }

    return sortedKeywords
  }

  // Function to get popular products based on search frequency
  const getPopularProducts = () => {
    // Default popular products
    const defaultProducts = [
      { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' }
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultProducts
    }

    // Map search frequency to products
    const productMapping: { [key: string]: { name: string, image: string } } = {
      'samsung galaxy': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'smartphone': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'sneakers': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'sepatu': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'tas selempang': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'tas': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'headphone': { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      'keyboard': { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      'power bank': { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      'smart tv': { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' }
    }

    // Get products based on search frequency
    const popularProducts: { name: string, image: string }[] = []
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)

    for (const [keyword] of sortedKeywords) {
      const lowerKeyword = keyword.toLowerCase()
      for (const [productKey, product] of Object.entries(productMapping)) {
        if (lowerKeyword.includes(productKey) && !popularProducts.some(p => p.name === product.name)) {
          popularProducts.push(product)
          if (popularProducts.length >= 8) break
        }
      }
      if (popularProducts.length >= 8) break
    }

    // Fill with defaults if needed
    while (popularProducts.length < 8) {
      const nextDefault = defaultProducts[popularProducts.length]
      if (nextDefault && !popularProducts.some(p => p.name === nextDefault.name)) {
        popularProducts.push(nextDefault)
      } else {
        break
      }
    }

    return popularProducts
  }

  // Check screen size for mobile detection
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Initialize search history and prediction history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('searchHistory')
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    } else {
      // Set default keywords jika tidak ada history (12 items untuk konsistensi)
      const defaultKeywords = [
        'tas pria', 'sepatu sneakers', 'smartphone android', 'headphone bluetooth',
        'keyboard gaming', 'mouse wireless', 'laptop gaming', 'power bank',
        'smart tv', 'kamera mirrorless', 'jam tangan pintar', 'speaker bluetooth'
      ]
      setSearchHistory(defaultKeywords)
      localStorage.setItem('searchHistory', JSON.stringify(defaultKeywords))
    }

    // Load prediction history
    loadUserInteractionHistory()

    // Load search frequency
    loadSearchFrequency()

    // Load successful search history
    loadSuccessfulSearchHistory()

    // Add event listener for subcategory search
    const handleSubcategorySearch = (event: CustomEvent) => {
      const { query, category, selectedSubcategory, allSubcategories, products } = event.detail;
      console.log('Received subcategory search event:', query, category, selectedSubcategory, allSubcategories);
      console.log('All subcategories received:', allSubcategories);
      console.log('Products received:', products);

      // Store subcategory context for facet panel
      const contextData = {
        category,
        selectedSubcategory,
        allSubcategories
      };

      (window as any).subcategoryContext = contextData;
      setSubcategoryContext(contextData);

      console.log('Stored subcategory context:', contextData);

      // FIXED: Expand search mode but don't change search value for subcategory
      // Show results in expanded overlay mode without affecting search box
      setIsSearchExpanded(true); // Enable expanded mode for overlay
      setShowSuggestions(false);
      setShowPredictions(false);
      setHideMainContent(true); // Hide main content to show results
      document.body.classList.remove('show-suggestions');
      document.body.classList.add('hide-main-content');

      // FIXED: Don't set search value - keep search box empty/unchanged
      // setSearchValue(query); // REMOVED

      // FIXED: Execute subcategory filter directly without search
      console.log('Filtering products for subcategory:', query);

      // Filter products by exact category match
      const results = sampleProducts.filter(product => {
        return product.category.toLowerCase() === query.toLowerCase()
      }).map(product => ({
        ...product,
        searchScore: 100, // High score for exact subcategory match
        matchDetails: [`exact subcategory match: ${query}`]
      }));

      console.log('Subcategory filter results:', results.length, 'produk ditemukan');
      console.log('🎯 Results:', results.map(r => `${r.name} (${r.category})`));

      // FIXED: Set original results to ALL products from the main category for proper filtering
      // Get all products from the main category, not just the selected subcategory
      const mainCategory = event.detail.category;
      const categorySubcategories = event.detail.allSubcategories;

      const allCategoryProducts = sampleProducts.filter(product => {
        if (!categorySubcategories || !Array.isArray(categorySubcategories)) return false;

        // Check if product belongs to any subcategory in this main category
        return categorySubcategories.some((sub: { name: string }) =>
          product.category.toLowerCase() === sub.name.toLowerCase()
        );
      }).map(product => ({
        ...product,
        searchScore: 100,
        matchDetails: [`category match: ${mainCategory}`]
      }));

      console.log('🔥 SUBCATEGORY: All category products for filtering:', allCategoryProducts.length);
      console.log('🔥 SUBCATEGORY: Products:', allCategoryProducts.map(p => `${p.name} (${p.category})`));
      console.log('🔥 SUBCATEGORY: Selected subcategory results:', results.length);
      console.log('🔥 SUBCATEGORY: Selected results:', results.map(p => `${p.name} (${p.category})`));

      // Set hasil filter - show selected subcategory initially
      setSearchResults(results);
      // Set original results to all category products for filtering
      setOriginalSearchResults(allCategoryProducts);
      setIsSearchResultShown(true);

      // Show filter tabs and icon only when results are found
      if (results.length > 0) {
        setShowFilterTabs(true);
        setActiveFilterTab('terkait');
        setShowFilterIcon(true);
      } else {
        setShowFilterTabs(false);
        setShowFilterIcon(false);
        setShowFacetPanel(false);
      }
    };

    window.addEventListener('subcategorySearch', handleSubcategorySearch as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('subcategorySearch', handleSubcategorySearch as EventListener);
    };
  }, [])

  // Function to clear search history
  const clearSearchHistory = () => {
    setSearchHistory([])
    localStorage.setItem('searchHistory', JSON.stringify([]))
    console.log("Riwayat pencarian dihapus")
  }

  // Function to get initial keywords (first 7 for button mode)
  const getInitialKeywords = () => {
    return searchHistory.slice(0, 7)
  }

  // Function to get extended keywords (12 terakhir untuk list mode)
  const getExtendedKeywords = () => {
    return searchHistory.slice(0, 12)
  }

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // Jangan auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    // Hide filter tabs and icon when input changes (typing or removing letters) - sesuai docs/facet.html
    if (showFilterTabs) {
      setShowFilterTabs(false)
      setShowFilterIcon(false) // Hide filter icon when typing
    }

    // Close facet panel when typing or removing letters
    if (showFacetPanel) {
      setShowFacetPanel(false)
    }

    // Jika sedang dalam mode hasil pencarian dan input berubah, kembali ke mode prediksi
    if (isSearchResultShown && value.trim().length >= 1) {
      setIsSearchResultShown(false)
      setSearchResults([])
      setHideMainContent(false) // Show main content again when returning to predictions
      setShowPredictions(true)
      setShowSuggestions(false)
      generatePredictions(value)
      document.body.classList.add('show-suggestions')
      return
    }

    // Show predictions when typing (minimal 1 character) and search is expanded
    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false) // Hide suggestions when predictions are shown
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      generatePredictions(value)
      document.body.classList.add('show-suggestions') // Use same class for overlay effect
    } else if (value.trim() === '' && isSearchExpanded) {
      // Show suggestions when input is empty
      setShowPredictions(false)
      setShowSuggestions(true)
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      setIsSearchResultShown(false)
      setSearchResults([])
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setShowPredictions(false) // Reset predictions
      setSearchResults([]) // Reset search results
      setIsSearchResultShown(false) // Reset search result state
      setHideMainContent(false) // Show main content again
      setSearchValue('')
      setShowMoreSuggestions(false) // Reset ke bentuk tombol
      setShowFacetPanel(false) // Close facet panel when collapsing search
      document.body.classList.remove('show-suggestions')
      document.body.classList.remove('hide-main-content') // Remove white background class
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      setShowPredictions(false) // Start with suggestions, not predictions
      setSearchResults([]) // Reset search results
      setIsSearchResultShown(false) // Reset search result state
      setHideMainContent(false) // Ensure main content is visible in suggestions mode
      setShowMoreSuggestions(false) // Pastikan bentuk tombol saat expand
      document.body.classList.add('show-suggestions')
    }
  }



  // Database keyword sekarang diimport dari file terpisah - satu sumber data tunggal

  // Data produk sekarang diimport dari file terpisah - satu sumber data tunggal

  // Function to generate predictions based on input - sesuai docs/facet.html
  const generatePredictions = (input: string) => {
    const inputLower = input.toLowerCase().trim()
    const words = inputLower.split(' ')

    // Menyimpan hasil berdasarkan kategori untuk memungkinkan duplikasi berdasarkan sumber
    let historyResults: any[] = []
    let productResults: any[] = []
    let relatedResults: any[] = []
    let synonymResults: any[] = []
    let correctionResults: any[] = []
    let trendingResults: any[] = []

    // 1. Tambahkan dari riwayat interaksi pengguna (maksimal 4)
    let historyCount = 0
    console.log('User interaction history:', userInteractionHistory)
    console.log('Input lower:', inputLower)

    for (let i = 0; i < userInteractionHistory.length && historyCount < 4; i++) {
      const item: string = userInteractionHistory[i]
      if (item && item.toLowerCase().includes(inputLower)) {
        historyResults.push({
          text: item,
          type: 'history',
          relevance: calculateRelevance(item, inputLower, 'history')
        })
        historyCount++
        console.log('Added history prediction:', item)
      }
    }

    console.log('History results:', historyResults)

    // 2. Tambahkan dari keyword produk
    keywordPredictionDB.productKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        productResults.push({
          text: keyword,
          type: 'product',
          relevance: calculateRelevance(keyword, inputLower, 'product')
        })
      }
    })

    // 3. Tambahkan trending keywords (maksimal 5) - dinamis berdasarkan search frequency
    const trendingKeywords = getTrendingKeywords().map(k => k.toLowerCase())
    trendingKeywords.forEach((keyword, index) => {
      if (keyword.toLowerCase().includes(inputLower)) {
        const baseRelevance = calculateRelevance(keyword, inputLower, 'trending')
        const positionBonus = (5 - index) * 5 // 25, 20, 15, 10, 5 poin tambahan
        trendingResults.push({
          text: keyword,
          type: 'trending',
          relevance: baseRelevance + positionBonus
        })
      }
    })

    // 4. Tambahkan dari related keywords
    words.forEach(word => {
      const relatedKeywords = keywordPredictionDB.relatedKeywords[word]
      if (relatedKeywords) {
        relatedKeywords.forEach((related: string) => {
          relatedResults.push({
            text: related,
            type: 'related',
            relevance: calculateRelevance(related, inputLower, 'related')
          })
        })
      }
    })

    // 5. Tambahkan dari sinonim
    words.forEach(word => {
      const synonyms = keywordPredictionDB.synonyms[word]
      if (synonyms) {
        synonyms.forEach((synonym: string) => {
          const newQuery = inputLower.replace(word, synonym)
          if (newQuery !== inputLower) {
            synonymResults.push({
              text: newQuery,
              type: 'synonym',
              relevance: calculateRelevance(newQuery, inputLower, 'synonym')
            })
          }
        })
      }
    })

    // 6. Periksa kemungkinan typo
    words.forEach(word => {
      const correction = keywordPredictionDB.typoCorrections[word]
      if (correction) {
        const corrected = inputLower.replace(word, correction)
        correctionResults.push({
          text: corrected,
          type: 'correction',
          relevance: calculateRelevance(corrected, inputLower, 'correction')
        })
      }
    })

    // Gabungkan semua hasil
    const allResults = [
      ...historyResults,
      ...productResults,
      ...trendingResults,
      ...relatedResults,
      ...synonymResults,
      ...correctionResults
    ]

    // Urutkan berdasarkan relevansi
    allResults.sort((a, b) => b.relevance - a.relevance)

    // Hapus duplikat berdasarkan text (tapi pertahankan yang relevance tertinggi)
    const uniqueResults: any[] = []
    const seenTexts = new Set()

    allResults.forEach(result => {
      if (!seenTexts.has(result.text.toLowerCase())) {
        seenTexts.add(result.text.toLowerCase())
        uniqueResults.push(result)
      }
    })

    // Batasi prediksi antara 4-12 item sesuai docs/facet.html
    const limitedPredictions = uniqueResults.slice(0, Math.max(4, Math.min(12, uniqueResults.length)))

    setPredictions(limitedPredictions)
  }

  // Function to calculate relevance score - sesuai docs/facet.html
  const calculateRelevance = (prediction: string, input: string, type: string) => {
    let score = 0

    // Bobot berdasarkan tipe - sesuai docs/facet.html
    const typeWeights = {
      'history': 100,
      'trending': 85,
      'product': 80,
      'correction': 75,
      'related': 60,
      'synonym': 50,
      'suggestion': 40
    }

    // Tambahkan bobot tipe
    score += typeWeights[type as keyof typeof typeWeights] || 0

    // Cari kata-kata dalam input
    const inputWords = input.toLowerCase().split(' ')
    const predictionWords = prediction.toLowerCase().split(' ')
    const predictionLower = prediction.toLowerCase()

    // Jika prediksi dimulai dengan input, tambahkan skor lebih tinggi
    if (predictionLower.startsWith(input.toLowerCase())) {
      score += 30
    }

    // Jika prediksi berisi input persis, tambahkan skor
    if (predictionLower.includes(input.toLowerCase())) {
      score += 20
    }

    // Bobot lebih tinggi untuk setiap kata dalam input yang ada dalam prediksi
    let matchingWords = 0
    inputWords.forEach(word => {
      if (word.length > 0) {
        // Cek jika ada kata dalam prediksi yang mengandung kata input ini
        const hasMatch = predictionWords.some(pw => pw.includes(word))
        if (hasMatch) {
          matchingWords++
          // Bobot lebih tinggi untuk kata yang lebih panjang
          score += word.length * 2 // Kata panjang lebih relevan
        }
      }
    })

    // Tambahkan skor berdasarkan persentase kata yang cocok
    score += (matchingWords / inputWords.length) * 25

    // Jika prediksi memiliki kata yang mengandung kata input paling panjang, beri bobot tinggi
    if (inputWords.length > 0) {
      // Urutkan kata input berdasarkan panjang (dari terpanjang)
      const sortedInputWords = [...inputWords].sort((a, b) => b.length - a.length)
      const longestWord = sortedInputWords[0]

      if (longestWord.length >= 2 && predictionLower.includes(longestWord)) {
        score += 40 // Bobot tinggi jika mengandung kata terpanjang
      }
    }

    // Skor lebih rendah untuk prediksi yang terlalu panjang
    if (prediction.length > input.length + 20) {
      score -= 10
    }

    return score
  }

  // Function to handle prediction click - sesuai docs/facet.html dengan auto search
  const handlePredictionClick = (prediction: Prediction) => {
    setSearchValue(prediction.text)
    setShowPredictions(false)
    setShowSuggestions(false)

    // Update search frequency for trending keywords
    updateSearchFrequency(prediction.text)

    // Add to user interaction history - sesuai docs/facet.html
    // Hindari duplikat dengan menghapus item yang sama
    const predictionText: string = prediction.text
    const newHistory = [...userInteractionHistory]
    const index = newHistory.indexOf(predictionText)
    if (index !== -1) {
      newHistory.splice(index, 1)
    }

    // Tambahkan ke awal array
    newHistory.unshift(predictionText)

    // Batasi jumlah item riwayat ke 20 sesuai docs/facet.html
    if (newHistory.length > 20) {
      newHistory.pop()
    }

    // Update state
    setUserInteractionHistory(newHistory)

    // Simpan ke localStorage jika tersedia
    try {
      localStorage.setItem('keywordPredictionHistory', JSON.stringify(newHistory))
      console.log('Saved prediction history to localStorage:', newHistory)
    } catch (e) {
      console.log('Failed to save prediction history to localStorage', e)
    }

    // Tambahkan ke search history juga - PERBAIKAN UTAMA
    if (!searchHistory.includes(predictionText)) {
      const newHistory = [predictionText, ...searchHistory.slice(0, 11)] // Maksimal 12 item
      setSearchHistory(newHistory)
      localStorage.setItem('searchHistory', JSON.stringify(newHistory))
      console.log('Added to search history:', predictionText)
    }

    // Jalankan pencarian otomatis - sesuai docs/facet.html
    executeSearch(prediction.text)

    console.log('Prediction clicked and search executed:', prediction.text)
  }

  // Function to execute search - sesuai docs/facet.html
  const executeSearch = (searchText?: string) => {
    const query = searchText || searchValue
    console.log('Melakukan pencarian untuk:', query)

    if (query.trim()) {
      // Sembunyikan prediksi dan suggestions
      setShowPredictions(false)
      setShowSuggestions(false)
      setHideMainContent(true) // Hide main content like docs/facet.html
      document.body.classList.remove('show-suggestions') // Remove overlay class
      document.body.classList.add('hide-main-content') // Add white background class

      // FIXED: Check if this is a subcategory search for exact matching
      let results: Product[] = []
      const isSubcategorySearch = subcategoryContext && subcategoryContext.selectedSubcategory === query

      if (isSubcategorySearch) {
        // For subcategory search, use exact category matching
        console.log('🎯 SUBCATEGORY SEARCH: Using exact matching for:', query)
        results = sampleProducts.filter(product => {
          return product.category.toLowerCase() === query.toLowerCase()
        }).map(product => ({
          ...product,
          searchScore: 100, // High score for exact subcategory match
          matchDetails: [`exact subcategory match: ${query}`]
        }))
      } else {
        // For regular search, use enhanced search
        results = enhancedSearch(query)
      }

      console.log('Hasil pencarian:', results.length, 'produk ditemukan')
      console.log('🎯 Search type:', isSubcategorySearch ? 'SUBCATEGORY' : 'REGULAR')
      console.log('🎯 Results:', results.map(r => `${r.name} (${r.category})`))

      // Set hasil pencarian
      setSearchResults(results)
      setOriginalSearchResults(results) // Store original results for filtering
      setIsSearchResultShown(true)

      // Show filter tabs and icon only when search results are found
      if (results.length > 0) {
        setShowFilterTabs(true)
        setActiveFilterTab('terkait') // Reset to default tab
        setShowFilterIcon(true) // Show filter icon after successful search

        // Save to successful search history - hanya jika menemukan produk
        saveSuccessfulSearch(query)
      } else {
        // Hide filter tabs and icon when no results found
        setShowFilterTabs(false)
        setShowFilterIcon(false)
        setShowFacetPanel(false) // Also hide facet panel
      }

      // Tambahkan ke search history
      if (!searchHistory.includes(query)) {
        const newHistory = [query, ...searchHistory.slice(0, 11)] // Maksimal 12 item
        setSearchHistory(newHistory)
        localStorage.setItem('searchHistory', JSON.stringify(newHistory))
      }

      // Tambahkan ke user interaction history untuk prediksi keyword
      const newUserHistory = [...userInteractionHistory]
      const existingIndex = newUserHistory.indexOf(query)
      if (existingIndex !== -1) {
        // Hapus yang lama dan pindahkan ke depan
        newUserHistory.splice(existingIndex, 1)
      }
      newUserHistory.unshift(query) // Tambahkan ke awal array

      // Batasi maksimal 20 item
      if (newUserHistory.length > 20) {
        newUserHistory.pop()
      }

      setUserInteractionHistory(newUserHistory)
      localStorage.setItem('keywordPredictionHistory', JSON.stringify(newUserHistory))
      console.log('Added to user interaction history:', query)
      console.log('Updated user interaction history:', newUserHistory)
    } else {
      // Jika pencarian kosong, reset ke tampilan awal
      setSearchResults([])
      setIsSearchResultShown(false)
      setHideMainContent(false) // Show main content again
      setShowSuggestions(true)
      setShowFilterTabs(false) // Hide filter tabs
      setShowFilterIcon(false) // Hide filter icon
      document.body.classList.add('show-suggestions')
      document.body.classList.remove('hide-main-content') // Remove white background class
    }
  }

  // Function to perform enhanced search with typo, synonym, and related keyword support
  const enhancedSearch = (query: string): Product[] => {
    const originalTerms = query.toLowerCase().trim().split(' ')
    const expandedTerms = new Set<string>()

    // Add original terms
    originalTerms.forEach(term => expandedTerms.add(term))

    // Expand search terms with typo corrections, synonyms, and related keywords
    originalTerms.forEach(term => {
      // 1. Check for typo corrections
      const correctedTerm = keywordPredictionDB.typoCorrections[term]
      if (correctedTerm) {
        expandedTerms.add(correctedTerm.toLowerCase())
        console.log(`Typo correction: ${term} -> ${correctedTerm}`)
      }

      // 2. Add synonyms
      const synonyms = keywordPredictionDB.synonyms[term]
      if (synonyms) {
        synonyms.forEach(synonym => expandedTerms.add(synonym.toLowerCase()))
        console.log(`Synonyms for ${term}:`, synonyms)
      }

      // 3. Add related keywords
      const relatedKeywords = keywordPredictionDB.relatedKeywords[term]
      if (relatedKeywords) {
        relatedKeywords.forEach(related => expandedTerms.add(related.toLowerCase()))
        console.log(`Related keywords for ${term}:`, relatedKeywords)
      }

      // 4. Check if term is a synonym of other words
      Object.entries(keywordPredictionDB.synonyms).forEach(([key, synonymList]) => {
        if (synonymList.some(syn => syn.toLowerCase() === term)) {
          expandedTerms.add(key.toLowerCase())
          synonymList.forEach(syn => expandedTerms.add(syn.toLowerCase()))
        }
      })
    })

    const searchTerms = Array.from(expandedTerms)
    console.log('Expanded search terms:', searchTerms)

    const results: Product[] = []

    sampleProducts.forEach(product => {
      let score = 0
      let matchDetails: string[] = []

      // Cek nama produk
      const productName = product.name.toLowerCase()
      const productShortName = product.shortName.toLowerCase()
      const productCategory = product.category.toLowerCase()
      const productWords = productName.split(' ')

      searchTerms.forEach(term => {
        // Exact match di nama produk (score tinggi)
        if (productName.includes(term)) {
          score += 15
          matchDetails.push(`exact match in name: ${term}`)
        }

        // Exact match di nama pendek
        if (productShortName.includes(term)) {
          score += 12
          matchDetails.push(`exact match in short name: ${term}`)
        }

        // Exact match di kategori - FIXED: Use exact matching for better precision
        if (productCategory === term || productCategory === query.toLowerCase()) {
          score += 15 // Higher score for exact category match
          matchDetails.push(`exact category match: ${term}`)
        } else if (productCategory.includes(term)) {
          score += 8 // Lower score for partial category match
          matchDetails.push(`partial category match: ${term}`)
        }

        // Partial match di awal kata
        productWords.forEach(word => {
          if (word.startsWith(term) && term.length >= 2) {
            score += 8
            matchDetails.push(`partial match: ${word} starts with ${term}`)
          }
        })

        // Fuzzy match untuk typo tolerance
        productWords.forEach(word => {
          if (calculateSimilarity(word, term) > 0.7 && term.length >= 3) {
            score += 5
            matchDetails.push(`fuzzy match: ${word} similar to ${term}`)
          }
        })
      })

      // Bonus untuk original terms (prioritas lebih tinggi)
      originalTerms.forEach(originalTerm => {
        if (productName.includes(originalTerm)) {
          score += 5 // Bonus untuk original term
          matchDetails.push(`original term bonus: ${originalTerm}`)
        }
      })

      // Jika ada score, tambahkan ke hasil
      if (score > 0) {
        results.push({
          ...product,
          searchScore: score,
          matchDetails: matchDetails
        })
      }
    })

    // Urutkan berdasarkan score (tertinggi dulu)
    const sortedResults = results.sort((a, b) => (b.searchScore || 0) - (a.searchScore || 0))

    console.log('Search results with scores:', sortedResults.map(r => ({
      name: r.name,
      score: r.searchScore,
      matches: r.matchDetails
    })))

    return sortedResults
  }

  // Helper function to calculate string similarity (Levenshtein distance based)
  const calculateSimilarity = (str1: string, str2: string): number => {
    const len1 = str1.length
    const len2 = str2.length

    if (len1 === 0) return len2 === 0 ? 1 : 0
    if (len2 === 0) return 0

    const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null))

    for (let i = 0; i <= len1; i++) matrix[i][0] = i
    for (let j = 0; j <= len2; j++) matrix[0][j] = j

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        )
      }
    }

    const maxLen = Math.max(len1, len2)
    return (maxLen - matrix[len1][len2]) / maxLen
  }

  // Handle suggestion click - auto search functionality
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)

    // Update search frequency for trending keywords
    updateSearchFrequency(suggestion)

    // Auto search functionality - execute search immediately
    executeSearch(suggestion)

    console.log('Suggestion clicked and auto search executed:', suggestion)
  }

  // Function to handle filter tab click
  const handleFilterTabClick = (filterType: string) => {
    setActiveFilterTab(filterType)

    if (filterType === 'harga') {
      // Toggle price sort direction
      setPriceSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    }

    // Apply sorting to current search results
    if (searchResults.length > 0) {
      const sortedResults = sortSearchResults(searchResults, filterType)
      setSearchResults(sortedResults)
    }
  }

  // Function to sort search results based on filter type
  const sortSearchResults = (results: Product[], filterType: string): Product[] => {
    const sortedResults = [...results]

    switch (filterType) {
      case 'terkait':
        // Sort by relevance score (default)
        return sortedResults.sort((a, b) => (b.searchScore || 0) - (a.searchScore || 0))

      case 'terlaris':
        // Sort by sold count
        return sortedResults.sort((a, b) => {
          const aSold = parseInt(a.sold.replace(/\D/g, '')) || 0
          const bSold = parseInt(b.sold.replace(/\D/g, '')) || 0
          return bSold - aSold
        })

      case 'terbaru':
        // Sort by product ID (assuming higher ID = newer)
        return sortedResults.sort((a, b) => b.id - a.id)

      case 'harga':
        // Sort by price
        return sortedResults.sort((a, b) => {
          const aPrice = parseInt(a.price.replace(/\D/g, '')) || 0
          const bPrice = parseInt(b.price.replace(/\D/g, '')) || 0
          return priceSortDirection === 'asc' ? aPrice - bPrice : bPrice - aPrice
        })

      default:
        return sortedResults
    }
  }

  // Function to handle filter icon click - sesuai docs/facet.html
  const handleFilterClick = () => {
    setShowFacetPanel(true)
  }

  // Function to handle facet panel close
  const handleFacetClose = () => {
    setShowFacetPanel(false)
  }

  // Function to update filter badge count
  const updateFilterBadgeCount = () => {
    const count = Object.values(facetFilters).reduce((total, filters) => total + filters.length, 0)
    setFilterBadgeCount(count)
  }

  // Function to generate facet data from search results - sesuai docs/facet.html
  const generateFacetData = (results: any[]) => {
    const facets = {
      categories: {} as {[key: string]: number},
      priceRanges: {} as {[key: string]: number},
      ratings: {} as {[key: string]: number},
      shipping: {} as {[key: string]: number},
      features: {} as {[key: string]: number}
    }

    // Hanya proses jika ada hasil pencarian
    if (results.length === 0) {
      return facets
    }

    results.forEach(product => {
      // Hitung kategori - hanya yang ada di hasil pencarian
      if (product.category) {
        facets.categories[product.category] = (facets.categories[product.category] || 0) + 1
      }

      // Hitung rentang harga - hanya yang ada di hasil pencarian
      const price = parseInt(product.price.replace(/\D/g, ''))
      if (price < 100000) {
        facets.priceRanges["Di bawah Rp 100.000"] = (facets.priceRanges["Di bawah Rp 100.000"] || 0) + 1
      } else if (price < 500000) {
        facets.priceRanges["Rp 100.000 - Rp 500.000"] = (facets.priceRanges["Rp 100.000 - Rp 500.000"] || 0) + 1
      } else if (price < 1000000) {
        facets.priceRanges["Rp 500.000 - Rp 1.000.000"] = (facets.priceRanges["Rp 500.000 - Rp 1.000.000"] || 0) + 1
      } else {
        facets.priceRanges["Di atas Rp 1.000.000"] = (facets.priceRanges["Di atas Rp 1.000.000"] || 0) + 1
      }

      // Hitung rating - hanya yang ada di hasil pencarian
      if (product.rating >= 5) {
        facets.ratings["5 Bintang"] = (facets.ratings["5 Bintang"] || 0) + 1
      } else if (product.rating >= 4) {
        facets.ratings["4+ Bintang"] = (facets.ratings["4+ Bintang"] || 0) + 1
      } else if (product.rating >= 3) {
        facets.ratings["3+ Bintang"] = (facets.ratings["3+ Bintang"] || 0) + 1
      }

      // Hitung pengiriman - hanya yang ada di hasil pencarian
      if (product.shipping) {
        facets.shipping[product.shipping] = (facets.shipping[product.shipping] || 0) + 1
      }

      // Hitung fitur - hanya yang ada di hasil pencarian
      if (product.cod) {
        facets.features["COD"] = (facets.features["COD"] || 0) + 1
      }
      if (product.isMall) {
        facets.features["SellZio Mall"] = (facets.features["SellZio Mall"] || 0) + 1
      }
    })

    return facets
  }

  // Function to generate keyword suggestions for popup - sesuai docs/facet.html
  const generateKeywordSuggestions = (): string[] => {
    const keywordSources: string[] = []

    // Sumber 1: Dari trending keywords (popularKeywords.getTopKeywords) - sesuai docs/facet.html
    const trendingKeywords = Object.entries(searchFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 4)
      .map(([keyword]) => keyword)
    keywordSources.push(...trendingKeywords)

    // Sumber 2: Dari kategori produk di sampleProducts - sesuai docs/facet.html
    const productCategories = [...new Set(sampleProducts.map(p => p.category))]
    keywordSources.push(...productCategories.slice(0, 3))

    // Sumber 3: Dari riwayat pencarian yang BERHASIL - sesuai docs/facet.html
    // Hanya gunakan pencarian yang berhasil menemukan produk
    const successfulSearchFiltered = successfulSearchHistory.slice(0, 6) // Ambil lebih banyak untuk filter
    const filteredSuccessfulHistory = successfulSearchFiltered.filter(keyword => {
      // Filter keyword yang terlalu pendek (kemungkinan typo)
      if (keyword.length < 3) return false

      // Filter keyword yang terlalu panjang (kemungkinan bukan keyword produk)
      if (keyword.length > 30) return false

      // Filter keyword yang tidak mengandung kata produk
      const productTerms = [
        'hp', 'phone', 'handphone', 'smartphone', 'sepatu', 'tas',
        'laptop', 'keyboard', 'mouse', 'headphone', 'tv', 'smart',
        'kamera', 'robot', 'vacuum', 'jam', 'speaker', 'bluetooth',
        'baju', 'celana', 'jaket', 'mainan', 'game', 'buku',
        'makeup', 'parfum', 'kulkas', 'mesin', 'komputer'
      ]

      // Cek apakah mengandung kata produk
      const hasProductTerm = productTerms.some(term =>
        keyword.toLowerCase().includes(term)
      )

      // Hanya terima jika mengandung kata produk
      return hasProductTerm
    })
    keywordSources.push(...filteredSuccessfulHistory.slice(0, 3))

    // Sumber 4: Dari nama pendek produk (shortNames) - UTAMA sesuai docs/facet.html
    const shortNames = [...new Set(sampleProducts.map(p => p.shortName))]
    keywordSources.push(...shortNames.slice(0, 4))

    // Hapus duplikat dan batasi jumlah
    const uniqueKeywords = [...new Set(keywordSources)]

    // FILTER AKHIR: Memastikan semua keyword valid - sesuai docs/facet.html
    const validKeywords = uniqueKeywords.filter(keyword => {
      // Pastikan bukan string kosong
      if (!keyword || keyword.trim() === '') return false

      // Filter keyword yang terlalu pendek
      if (keyword.length < 3) return false

      // Filter keyword yang terlihat seperti typo (hanya karakter berulang)
      const repeatedCharsPattern = /^(.)\1+$/
      if (repeatedCharsPattern.test(keyword)) return false

      // Filter keyword yang tidak memiliki vokal (kemungkinan typo)
      const hasVowel = /[aeiouy]/i.test(keyword)
      if (!hasVowel) return false

      // Filter keyword dengan terlalu banyak karakter aneh
      const specialCharsCount = (keyword.match(/[^a-zA-Z0-9\s]/g) || []).length
      if (specialCharsCount > 2) return false

      return true
    })

    // Acak urutan keyword untuk variasi - sesuai docs/facet.html
    const shuffledKeywords = validKeywords.sort(() => 0.5 - Math.random())
    const displayKeywords = shuffledKeywords.slice(0, 8) // Tampilkan maksimal 8 keyword

    // Jika tidak ada keyword valid yang tersisa, tambahkan beberapa default
    if (displayKeywords.length === 0) {
      const defaultKeywords = [
        "Smartphone Android", "Sepatu Sneakers", "Tas Selempang",
        "Headphone Bluetooth", "Keyboard Gaming", "Power Bank",
        "Smart TV", "Robot Vacuum"
      ]
      return defaultKeywords
    }

    return displayKeywords
  }

  // Function to handle keyword suggestion click in popup
  const handleKeywordSuggestionClick = (keyword: string) => {
    // Hide popup
    setShowSuggestionsPopup(false)

    // Fill search input
    setSearchValue(keyword)

    // Execute search
    setTimeout(() => {
      executeSearch(keyword)
    }, 50)
  }

  // FIXED: Function to get all products from sampleProducts - SATU SUMBER DATA
  const getAllAllProducts = () => {
    console.log('🔥 DEBUG: getAllAllProducts called - using sampleProducts');
    console.log('🔥 DEBUG: sampleProducts length:', sampleProducts.length);
    console.log('🔥 DEBUG: First few products:', sampleProducts.slice(0, 3).map(p => `${p.name} (${p.category})`));
    return sampleProducts; // Menggunakan sampleProducts sebagai satu-satunya sumber data
  };

  // Function to apply filters to search results
  const applyFilters = (results: any[], filters: {[key: string]: string[]}) => {
    console.log('🎯 FILTER: Starting applyFilters with:', {
      resultsCount: results.length,
      filters,
      hasKategoriFilter: !!filters.kategori,
      kategoriLength: filters.kategori?.length || 0
    });

    const context = subcategoryContext || (window as any).subcategoryContext;

    // FIXED: Enhanced handling for subcategory context
    if (context && context.allSubcategories && filters.kategori) {
      console.log('🎯 FILTER: In subcategory context with filters');

      // If no filters selected at all, show all products from current context
      if (filters.kategori.length === 0) {
        console.log('🎯 FILTER: No filters selected, showing all products from context');
        return results;
      }

      // Check if any subcategory is selected (not main category)
      const hasSubcategorySelected = context.allSubcategories.some((sub: any) =>
        filters.kategori.includes(sub.name)
      );

      // Check if main category is selected
      const hasMainCategorySelected = filters.kategori.includes(context.category);

      console.log('🎯 FILTER: Selection status:', {
        hasSubcategorySelected,
        hasMainCategorySelected,
        selectedFilters: filters.kategori,
        availableSubcategories: context.allSubcategories.map((s: any) => s.name)
      });

      // FIXED: If main category is selected but no subcategories, show warning
      // If subcategories are selected, proceed with filtering
      if (!hasSubcategorySelected && hasMainCategorySelected) {
        console.log('🎯 FILTER: Only main category selected, need subcategory selection');
        return []; // This will trigger warning message
      }
    }

    const filteredResults = results.filter(product => {
      console.log('🔍 FILTER: Processing product:', {
        name: product.name,
        category: product.category,
        price: product.price,
        filters: Object.keys(filters)
      });

      // Filter berdasarkan kategori - enhanced untuk subcategory
      if (filters.kategori && filters.kategori.length > 0) {
        // FIXED: Enhanced multiple subcategory selection support
        const matchesCategory = filters.kategori.some(filterCategory => {
          const filterCat = filterCategory.toLowerCase();
          const productName = product.name?.toLowerCase() || '';
          const productCategory = product.category?.toLowerCase() || '';

          console.log('🔍 FILTER: Checking product:', {
            productName: product.name,
            productCategory: product.category,
            filterCategory,
            filterCat
          });

          // FIXED: Primary exact category matching (case insensitive)
          if (productCategory === filterCat) {
            console.log('✅ FILTER: Exact category match');
            return true;
          }

          // FIXED: If main category is selected, show all products from subcategories in that category
          if (context && filterCategory === context.category) {
            const belongsToMainCategory = context.allSubcategories.some((sub: any) =>
              productCategory === sub.name.toLowerCase()
            );
            console.log('🏷️ FILTER: Main category check:', {
              belongsToMainCategory,
              availableSubcategories: context.allSubcategories.map((s: any) => s.name.toLowerCase())
            });
            return belongsToMainCategory;
          }

          // FIXED: Enhanced subcategory matching - check if filterCategory is a subcategory name
          if (context && context.allSubcategories) {
            const isSubcategoryFilter = context.allSubcategories.some((sub: any) =>
              sub.name.toLowerCase() === filterCat
            );

            if (isSubcategoryFilter) {
              // This is a subcategory filter, use exact matching
              const exactMatch = productCategory === filterCat;
              console.log('🎯 FILTER: Subcategory filter check:', {
                filterCategory,
                productCategory: product.category,
                exactMatch
              });
              return exactMatch;
            }
          }

          // FIXED: Enhanced matching for specific subcategories with better logic
          if (filterCat.includes('konsol') || filterCat.includes('game')) {
            const isGameProduct = productName.includes('playstation') || productName.includes('ps') ||
                productName.includes('xbox') || productName.includes('nintendo') ||
                productName.includes('steam') || productName.includes('switch') ||
                productName.includes('game') || productName.includes('konsol');
            if (isGameProduct) {
              console.log('🎮 FILTER: Game product match');
              return true;
            }
          }

          if (filterCat.includes('aksesoris')) {
            const isAccessory = productName.includes('controller') || productName.includes('headset') ||
                productName.includes('mouse') || productName.includes('keyboard') ||
                productName.includes('gamepad') || productName.includes('joystick') ||
                productName.includes('aksesoris') || productName.includes('accessory');
            if (isAccessory) {
              console.log('🎧 FILTER: Accessory product match');
              return true;
            }
          }

          // Fallback: partial matching for compatibility (moved after specific matching)
          if (productName.includes(filterCat) || productCategory.includes(filterCat)) {
            console.log('🔄 FILTER: Fallback partial match');
            return true;
          }

          if (filterCat.includes('casing') || filterCat.includes('case')) {
            if (productName.includes('case') || productName.includes('casing') ||
                productName.includes('cover') || productName.includes('housing')) return true;
          }

          if (filterCat.includes('foot bath') || filterCat.includes('spa')) {
            if (productName.includes('foot') || productName.includes('spa') ||
                productName.includes('bath') || productName.includes('massage')) return true;
          }

          if (filterCat.includes('mesin jahit')) {
            if (productName.includes('sewing') || productName.includes('jahit') ||
                productName.includes('mesin')) return true;
          }

          if (filterCat.includes('setrika') || filterCat.includes('mesin uap')) {
            if (productName.includes('iron') || productName.includes('setrika') ||
                productName.includes('steam') || productName.includes('uap')) return true;
          }

          if (filterCat.includes('purifier') || filterCat.includes('humidifier')) {
            if (productName.includes('purifier') || productName.includes('humidifier') ||
                productName.includes('air') || productName.includes('filter')) return true;
          }

          if (filterCat.includes('telepon')) {
            if (productName.includes('phone') || productName.includes('telepon') ||
                productName.includes('telephone')) return true;
          }

          if (filterCat.includes('cuci') || filterCat.includes('pengering')) {
            if (productName.includes('wash') || productName.includes('cuci') ||
                productName.includes('dryer') || productName.includes('pengering')) return true;
          }

          // Check if product matches main category (fallback for non-subcategory contexts)
          if (!context && product.category === filterCategory) {
            console.log('📂 FILTER: Non-context category match');
            return true;
          }

          console.log('❌ FILTER: No match found');
          return false;
        });

        if (!matchesCategory) {
          console.log('🚫 FILTER: Product filtered out - no category match');
          return false;
        } else {
          console.log('✅ FILTER: Product passed category filter');
        }
      }

      // Filter berdasarkan rentang harga
      if (filters['rentang harga'] && filters['rentang harga'].length > 0) {
        const price = parseInt(product.price.replace(/\D/g, ''))
        console.log('💰 PRICE FILTER: Checking product:', {
          productName: product.name,
          originalPrice: product.price,
          parsedPrice: price,
          priceFilters: filters['rentang harga']
        });

        const inRange = filters['rentang harga'].some((range: string) => {
          let rangeMatch = false;
          if (range === "Di bawah Rp 100.000") {
            rangeMatch = price < 100000;
          } else if (range === "Rp 100.000 - Rp 500.000") {
            rangeMatch = price >= 100000 && price < 500000;
          } else if (range === "Rp 500.000 - Rp 1.000.000") {
            rangeMatch = price >= 500000 && price < 1000000;
          } else if (range === "Rp 1.000.000 - Rp 5.000.000") {
            rangeMatch = price >= 1000000 && price < 5000000;
          } else if (range === "Di atas Rp 5.000.000") {
            rangeMatch = price >= 5000000;
          }

          console.log(`💰 PRICE FILTER: Range "${range}" - Price ${price} - Match: ${rangeMatch}`);
          return rangeMatch;
        })

        console.log('💰 PRICE FILTER: Final price range result:', inRange);
        if (!inRange) {
          console.log('🚫 PRICE FILTER: Product filtered out due to price range');
          return false;
        } else {
          console.log('✅ PRICE FILTER: Product passed price range filter');
        }
      }

      // Filter berdasarkan rating
      if (filters.rating && filters.rating.length > 0) {
        const productRating = typeof product.rating === 'string' ? parseFloat(product.rating) : product.rating
        const meetsRating = filters.rating.some((rating: string) => {
          if (rating === "5 Bintang")
            return productRating >= 5
          else if (rating === "4 Bintang ke atas")
            return productRating >= 4
          else if (rating === "3 Bintang ke atas")
            return productRating >= 3
          return false
        })

        if (!meetsRating) return false
      }

      // Filter berdasarkan pengiriman
      if (filters.pengiriman && filters.pengiriman.length > 0) {
        const hasShippingMatch = filters.pengiriman.some((shipping: string) => {
          if (shipping === "Gratis Ongkir") {
            return product.shipping === "Gratis Ongkir"
          }
          if (shipping === "Same Day") {
            return product.shipping === "Same Day"
          }
          if (shipping === "Next Day") {
            return product.shipping === "Next Day"
          }
          return product.shipping && product.shipping.includes(shipping)
        })
        if (!hasShippingMatch) return false
      }

      // Filter berdasarkan fitur
      if (filters.fitur && filters.fitur.length > 0) {
        const hasFeature = filters.fitur.some((feature: string) => {
          if (feature === "COD") return product.cod === true
          if (feature === "SellZio Mall") return product.isMall === true
          if (feature === "Flash Sale") return product.flashSale === true
          return false
        })

        if (!hasFeature) return false
      }

      return true
    })

    console.log('🎯 FILTER: Final results:', {
      originalCount: results.length,
      filteredCount: filteredResults.length,
      filteredProducts: filteredResults.map(p => `${p.name} (${p.category})`)
    });

    return filteredResults
  }



  // Function to handle clear search - sesuai docs/facet.html
  const handleClearSearch = () => {
    setShowFilterIcon(false) // Hide filter icon when clearing search
    setShowFilterTabs(false) // Hide filter tabs when clearing search
    setSearchResults([])
    setIsSearchResultShown(false)
    setHideMainContent(false)
    setFacetFilters({}) // Clear all filters
    setFilterBadgeCount(0) // Reset badge count
    document.body.classList.add('show-suggestions')
    document.body.classList.remove('hide-main-content')
  }

  // Update filter badge count when facetFilters change
  useEffect(() => {
    updateFilterBadgeCount()
  }, [facetFilters])

  // ADDED: Event listener untuk komunikasi dengan categories.tsx
  useEffect(() => {
    const handleGetProductsByCategory = (event: CustomEvent) => {
      const { category } = event.detail;
      console.log('🔥 PAGE: Received getProductsByCategory event for:', category);

      // Filter produk berdasarkan category dari sampleProducts
      const filteredProducts = sampleProducts.filter(product =>
        product.category === category ||
        product.name.toLowerCase().includes(category.toLowerCase())
      );

      console.log('🔥 PAGE: Found products:', filteredProducts.length, 'for category:', category);

      // Dispatch response event dengan data produk
      const responseEvent = new CustomEvent('categoryProductsResponse', {
        detail: { category, products: filteredProducts }
      });
      window.dispatchEvent(responseEvent);
    };

    // Add event listener
    window.addEventListener('getProductsByCategory', handleGetProductsByCategory as EventListener);

    return () => {
      window.removeEventListener('getProductsByCategory', handleGetProductsByCategory as EventListener);
    };
  }, [sampleProducts]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
      document.body.classList.remove('hide-main-content')
      setShowPredictions(false)
      setShowSuggestions(false)
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className={`min-h-screen bg-white ${hideMainContent ? 'hide-main-content' : ''}`}>
        {/* Overlay hanya untuk suggestions dan predictions, TIDAK untuk search results */}
        {(showSuggestions || showPredictions) && !isSearchResultShown && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onSearchExecute={executeSearch}
        onClearSearch={handleClearSearch}
        onToggleExpanded={handleToggleExpanded}
        showFilterIcon={showFilterIcon && isMobile}
        onFilterClick={handleFilterClick}
        filterBadgeCount={filterBadgeCount}
      />

      {/* Categories - hanya tampil saat tidak ada search results dan tidak dalam mode search */}
      {!isSearchResultShown && !isSearchExpanded && !showSuggestions && !showPredictions && (
        <SellzioCategories />
      )}

      {/* Filter Tabs - positioned in header area like docs/facet.html */}
      {showFilterTabs && (
        <div className="filter-tabs-header">
          <div className="filter-tabs-inner">
            <button
              className={`filter-tab ${activeFilterTab === 'terkait' ? 'active' : ''}`}
              onClick={() => handleFilterTabClick('terkait')}
            >
              Terkait
            </button>
            <button
              className={`filter-tab ${activeFilterTab === 'terlaris' ? 'active' : ''}`}
              onClick={() => handleFilterTabClick('terlaris')}
            >
              Terlaris
            </button>
            <button
              className={`filter-tab ${activeFilterTab === 'terbaru' ? 'active' : ''}`}
              onClick={() => handleFilterTabClick('terbaru')}
            >
              Terbaru
            </button>
            <button
              className={`filter-tab filter-tab-price ${activeFilterTab === 'harga' ? 'active' : ''}`}
              onClick={() => handleFilterTabClick('harga')}
            >
              Harga <i className={`fa ${activeFilterTab === 'harga' ? (priceSortDirection === 'asc' ? 'fa-arrow-up' : 'fa-arrow-down') : 'fa-sort'}`}></i>
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="main-content pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>



          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Keyword Predictions Container - Muncul saat mengetik minimal 1 huruf */}
      {showPredictions && (
        <div className="keyword-predictions" onClick={(e) => e.stopPropagation()}>
          {predictions.map((prediction, index) => {
            // Determine icon based on prediction type
            let iconClass = 'fa-search'
            if (prediction.type === 'history') {
              iconClass = 'fa-history'
            } else if (prediction.type === 'product') {
              iconClass = 'fa-shopping-cart'
            } else if (prediction.type === 'trending') {
              iconClass = 'fa-arrow-trend-up'
            }

            // Check if prediction contains main keyword from input
            const containsMainKeyword = (input: string, predictionText: string) => {
              const inputWords = input.toLowerCase().trim().split(' ')
              const mainWord = inputWords[0]
              if (mainWord && mainWord.length >= 2) {
                return predictionText.toLowerCase().includes(mainWord)
              }
              return false
            }

            const isRelevant = containsMainKeyword(searchValue, prediction.text)

            // Create highlighted text
            const highlightText = (text: string, input: string) => {
              if (!input.trim()) return text

              try {
                // Escape special regex characters
                const escapedInput = input.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                const regex = new RegExp(`(${escapedInput})`, 'gi')
                const parts = text.split(regex)

                return parts.map((part, i) =>
                  regex.test(part) ?
                    <span key={i} className="highlighted">{part}</span> :
                    part
                )
              } catch (e) {
                // Fallback if regex fails
                return text
              }
            }

            return (
              <div
                key={index}
                className="prediction-item"
                onClick={() => handlePredictionClick(prediction)}
              >
                <span className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
                  <i className={`fa ${iconClass}`}></i>
                </span>
                <span className="prediction-text">
                  {highlightText(toTitleCase(prediction.text), searchValue)}
                </span>
              </div>
            )
          })}
        </div>
      )}

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={clearSearchHistory}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Keyword suggestions di bagian atas - SEBELUM klik "Lihat Lainnya" = bentuk tombol dengan icon */}
          {!showMoreSuggestions && searchHistory.length > 0 && (
            <div className="keyword-button-container">
              {getInitialKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="keyword-button"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <span className="suggestion-icon">
                    <i className="fa fa-history"></i>
                  </span>
                  <span className="keyword-button-text">{keyword}</span>
                </div>
              ))}
            </div>
          )}

          {/* Show empty message if no search history */}
          {!showMoreSuggestions && searchHistory.length === 0 && (
            <div className="empty-history-message">
              <p>Belum ada riwayat pencarian</p>
            </div>
          )}

          {/* Keyword suggestions di bagian atas - SETELAH klik "Lihat Lainnya" = bentuk list dinamis dari 12 terakhir search history */}
          {showMoreSuggestions && (
            <div className="main-keyword-suggestions-list">
              {getExtendedKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">{keyword}</span>
                </div>
              ))}

              {/* Show message if no extended history */}
              {getExtendedKeywords().length === 0 && (
                <div className="empty-history-message">
                  <p>Belum ada riwayat pencarian tambahan</p>
                </div>
              )}
            </div>
          )}

          {/* Tombol Lihat Lainnya untuk keyword suggestions - hanya tampil jika ada lebih dari 7 items */}
          {searchHistory.length > 7 && (
            <div className="main-see-more-container">
              <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
                {!showMoreSuggestions ? (
                  <>
                    <i className="fas fa-plus-circle"></i>
                    <span>Lihat Lainnya</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-minus-circle"></i>
                    <span>Sembunyikan</span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Trending keywords - selalu dalam bentuk list dengan icon trend */}
            <div className="trending-keywords-list">
              {getTrendingKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">{keyword}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Produk Populer section - bentuk card dengan image dan judul */}
          <div className="product-suggestions">
            <div className="product-title">Produk Populer</div>
            <div className="product-grid">
              {getPopularProducts().map((product, index) => (
                <div
                  key={index}
                  className="simple-product-card"
                  onClick={() => handleSuggestionClick(product.name)}
                >
                  <img src={product.image} alt={product.name} className="product-img" />
                  <div className="simple-product-name">{product.name}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Search Results Container with Sidebar Layout for Desktop */}
      {isSearchResultShown && (
        <>
          {(() => {
            // FIXED: Check if we're in subcategory context and need to show warning
            const context = subcategoryContext || (window as any).subcategoryContext;
            const hasSubcategorySelected = context && context.allSubcategories?.some((sub: any) =>
              facetFilters.kategori?.includes(sub.name)
            );
            const hasMainCategorySelected = context && facetFilters.kategori?.includes(context.category);

            console.log('🎯 PAGE: Warning check:', {
              context: !!context,
              allSubcategories: context?.allSubcategories?.length || 0,
              facetFilters: facetFilters,
              hasSubcategorySelected,
              hasMainCategorySelected,
              searchResultsLength: searchResults.length
            });

            // FIXED: Show warning when we're in subcategory context and NO categories selected at all
            // This should happen when:
            // 1. We have subcategory context (came from category click)
            // 2. NO main category selected AND no subcategories selected (unceklis semua)
            const shouldShowSubcategoryMessage = context && context.allSubcategories && !hasMainCategorySelected && !hasSubcategorySelected;

            console.log('🎯 PAGE: Should show subcategory message:', shouldShowSubcategoryMessage);
            console.log('🎯 PAGE: Detailed check:', {
              hasContext: !!context,
              hasAllSubcategories: !!(context && context.allSubcategories),
              hasKategoriFilter: !!(facetFilters.kategori),
              kategoriLength: facetFilters.kategori?.length || 0,
              hasSubcategorySelected,
              kategoriFilters: facetFilters.kategori
            });

            if (searchResults.length === 0) {
              if (shouldShowSubcategoryMessage) {
                // Show "Pilih Subcategory" message with facet panel still visible
                return (
                  <div className="search-results-layout">
                    {/* Desktop Facet Sidebar - Keep visible */}
                    <div className="desktop-facet-sidebar">
                      <SellzioFacet
                        searchResults={originalSearchResults}
                        displayedProducts={searchResults}
                        activeFilters={facetFilters}
                        onFiltersChange={(filters) => {
                          console.log('🎯 PAGE: Received filters from facet:', filters);
                          setFacetFilters(filters as { [key: string]: string[] })
                          const count = Object.values(filters).reduce((total, values) => total + (values?.length || 0), 0)
                          setFilterBadgeCount(count)
                          if (originalSearchResults.length > 0) {
                            console.log('🎯 PAGE: Applying filters to', originalSearchResults.length, 'products');
                            const filteredResults = applyFilters(originalSearchResults, filters as { [key: string]: string[] })
                            console.log('🎯 PAGE: Filtered results:', filteredResults.length, 'products');
                            setSearchResults(filteredResults)
                          }
                        }}
                        isVisible={true}
                        onClose={() => {}}
                        isDesktopSidebar={true}
                        allProducts={getAllAllProducts()}
                        subcategoryContext={subcategoryContext}
                      />
                    </div>

                    {/* Subcategory Selection Message */}
                    <div className="search-results-container">
                      <div className="subcategory-selection-message">
                        <div className="subcategory-message-icon">
                          <div className="category-icon">
                            <i className="fa fa-list"></i>
                          </div>
                        </div>
                        <div className="subcategory-message-title">Pilih Subcategory</div>
                        <div className="subcategory-message-text">
                          Silakan pilih subcategory dari <strong>{(subcategoryContext || (window as any).subcategoryContext)?.category || 'kategori ini'}</strong> untuk melihat produk yang tersedia.
                        </div>
                      </div>
                    </div>
                  </div>
                );
              } else {
                // Show regular "Tidak Ditemukan" layout
                return (
                  <div className="not-found-full-layout">
                    <div className="not-found-container">
                      <div className="not-found-icon">
                        <div className="search-document-icon">
                          <div className="document-base"></div>
                          <div className="document-fold"></div>
                          <div className="document-lines"></div>
                          <div className="magnifying-glass"></div>
                        </div>
                      </div>
                      <div className="not-found-title">Hasil tidak ditemukan</div>
                      <div className="not-found-message">Mohon coba kata kunci yang lain atau yang lebih umum.</div>
                <div className="not-found-button-container">
                  <button className="not-found-button primary" onClick={() => {
                    // Deactivate special modes - sesuai docs/facet.html
                    setShowFilterTabs(false)
                    setShowFilterIcon(false)
                    setShowFacetPanel(false)

                    // Clear search input - sesuai docs/facet.html
                    setSearchValue('')
                    setSearchResults([])
                    setIsSearchResultShown(false)
                    setHideMainContent(false)
                    setIsSearchExpanded(true) // Keep expanded for new input
                    setShowSuggestions(true) // Show suggestions like docs/facet.html
                    setShowPredictions(false) // Reset predictions

                    // Reset body classes
                    document.body.classList.remove('hide-main-content')
                    document.body.classList.add('show-suggestions')

                    // Focus to input - sesuai docs/facet.html
                    setTimeout(() => {
                      const searchInput = document.querySelector('.search-input-expanded') as HTMLInputElement
                      if (searchInput) {
                        searchInput.focus()
                      }
                    }, 100)
                  }}>
                    Coba kata kunci lain
                  </button>
                  <button className="not-found-button secondary" onClick={() => {
                    // Show suggestions popup like docs/facet.html
                    setShowSuggestionsPopup(true)

                    // Hide other elements
                    setIsSearchResultShown(false)
                    setShowFilterTabs(false)
                    setShowFilterIcon(false)
                    setShowFacetPanel(false)
                    setShowSuggestions(false)
                    setShowPredictions(false)

                    // Show overlay for popup
                    document.body.classList.add('show-suggestions')
                  }}>
                    Coba produk lainnya
                  </button>
                </div>
              </div>
            </div>
                );
              }
            } else {
              // Show search results
              return (
            /* Sidebar Layout for Search Results */
            <div className="search-results-layout">
              {/* Desktop Facet Sidebar - Only visible when search results found */}
              <div className="desktop-facet-sidebar">
                <SellzioFacet
                  searchResults={originalSearchResults}
                  displayedProducts={searchResults}
                  activeFilters={facetFilters}
                  onFiltersChange={(filters) => {
                    setFacetFilters(filters as { [key: string]: string[] })
                    // Update filter badge count
                    const count = Object.values(filters).reduce((total, values) => total + (values?.length || 0), 0)
                    setFilterBadgeCount(count)
                    // Apply filters to search results
                    if (originalSearchResults.length > 0) {
                      const filteredResults = applyFilters(originalSearchResults, filters as { [key: string]: string[] })
                      setSearchResults(filteredResults)
                    }
                  }}
                  isVisible={true} // Always visible for desktop sidebar
                  onClose={() => {}} // No close functionality for desktop sidebar
                  isDesktopSidebar={true} // New prop to indicate this is desktop sidebar
                  allProducts={getAllAllProducts()}
                  subcategoryContext={subcategoryContext}
                />
              </div>

              {/* Search Results Content */}
              <div className="search-results-container">
                <div className="search-results-grid">
                  {searchResults.map((product) => (
                    <div key={product.id} className="search-result-card">
                      <img src={product.image} alt={product.name} className="result-product-img" />
                      <div className="result-product-info">
                        <div className="result-product-name">
                          {product.isMall && <span className="sellzio-mall-icon">Mall</span>}
                          {product.name}
                        </div>
                        <div className="result-product-rating-sold">
                          <div className="result-product-rating">
                            <i className="fa fa-star"></i>
                            {product.rating}
                          </div>
                          <div className="result-product-sold">Terjual {product.sold}</div>
                        </div>
                        <div className="result-product-shipping">
                          <i className="fa fa-truck"></i>
                          {product.shipping}
                        </div>
                        <div className="result-product-price-container">
                          <div className="result-product-price">{product.price}</div>
                          <div className="result-product-price-original">{product.originalPrice}</div>
                          <div className="result-product-discount">-{product.discount}</div>
                        </div>
                      </div>
                      {product.cod && (
                        <div className="result-cod-icon">
                          <i className="fa fa-money-bill"></i>
                          COD
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
              );
            }
          })()}
        </>
      )}




      </div>

      {/* CSS Styles for Filter Tabs in Header */}
      <style jsx>{`
        .filter-tabs-header {
          position: fixed;
          top: 60px;
          left: 0;
          right: 0;
          z-index: 1002;
          background-color: white;
          border-bottom: 1px solid #f2f2f2;
          height: 45px;
          display: flex;
          align-items: center;
          overflow-x: visible;
          -webkit-overflow-scrolling: touch;
        }

        .filter-tabs-inner {
          display: inline-flex;
          padding: 0 10px;
          padding-left: 25px;
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        }

        .filter-tab {
          padding: 12px 15px;
          font-size: 14px;
          color: #666;
          cursor: pointer;
          position: relative;
          transition: color 0.2s ease;
          background: none;
          border: none;
          white-space: nowrap;
          flex: 1;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .filter-tab.active {
          color: #ee4d2d;
          font-weight: 500;
        }

        .filter-tab.active:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 15px;
          right: 15px;
          height: 2px;
          background-color: #ee4d2d;
        }

        .filter-tab-price {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
        }

        .filter-tab-price i {
          font-size: 12px;
          margin: 0;
        }

        /* Adjust main content when filter tabs are shown */
        .search-results-container {
          margin-top: ${showFilterTabs ? '45px' : '0'};
          padding-top: 20px;
        }

        /* Adjust layout when filter tabs are shown */
        .search-results-layout {
          margin-top: ${showFilterTabs ? '90px' : '50px'} !important;
        }

        /* Mobile responsive - sesuai docs/facet.html */
        @media (max-width: 480px) {
          .filter-tabs-header {
            display: flex;
            justify-content: center;
            padding: 0;
            width: 100%;
          }

          .filter-tabs-inner {
            max-width: 800px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0;
            margin: 0 auto;
          }

          .filter-tab {
            flex: 1;
            text-align: center;
            white-space: nowrap;
            padding: 12px 8px;
            font-size: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .filter-tab.filter-tab-price {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
          }

          .filter-tab-price i {
            margin: 0;
            font-size: 11px;
          }
        }






        /* Subcategory Selection Message Styles */
        .subcategory-selection-message {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 400px;
          padding: 40px 20px;
          text-align: center;
        }

        .subcategory-message-icon {
          margin-bottom: 24px;
        }

        .category-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ee4d2d 0%, #ff6b35 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          box-shadow: 0 8px 24px rgba(238, 77, 45, 0.3);
        }

        .category-icon i {
          font-size: 32px;
          color: white;
        }

        .subcategory-message-title {
          font-size: 24px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
        }

        .subcategory-message-text {
          font-size: 16px;
          color: #666;
          line-height: 1.5;
          max-width: 400px;
        }

        .subcategory-message-text strong {
          color: #ee4d2d;
          font-weight: 600;
        }

        /* Mobile responsive for subcategory message */
        @media (max-width: 768px) {
          .subcategory-selection-message {
            min-height: 300px;
            padding: 30px 15px;
          }

          .category-icon {
            width: 60px;
            height: 60px;
          }

          .category-icon i {
            font-size: 24px;
          }

          .subcategory-message-title {
            font-size: 20px;
          }

          .subcategory-message-text {
            font-size: 14px;
          }
        }

      `}</style>

      {/* Keyword Suggestions Popup - sesuai docs/facet.html */}
      {showSuggestionsPopup && (
        <>
          {/* Overlay */}
          <div
            className="suggestions-overlay"
            onClick={() => setShowSuggestionsPopup(false)}
          ></div>

          {/* Popup Content */}
          <div className="keyword-suggestions-popup">
            <div className="keyword-suggestions-title">Produk Populer</div>
            <div className="keyword-suggestions-grid">
              {generateKeywordSuggestions().map((keyword, index) => (
                <div
                  key={index}
                  className="keyword-suggestion-tag"
                  onClick={() => handleKeywordSuggestionClick(keyword)}
                >
                  {keyword}
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Mobile/Tablet Facet Component - Only for mobile/tablet popup and when results exist */}
      {searchResults.length > 0 && (
        <SellzioFacet
          searchResults={originalSearchResults}
          displayedProducts={searchResults}
          activeFilters={facetFilters}
          onFiltersChange={(filters) => {
            setFacetFilters(filters as { [key: string]: string[] })
            // Update filter badge count
            const count = Object.values(filters).reduce((total, values) => total + (values?.length || 0), 0)
            setFilterBadgeCount(count)
            // Apply filters to search results
            if (originalSearchResults.length > 0) {
              const filteredResults = applyFilters(originalSearchResults, filters as { [key: string]: string[] })
              setSearchResults(filteredResults)
            }
          }}
          isVisible={showFacetPanel}
          onClose={() => setShowFacetPanel(false)}
          isDesktopSidebar={false} // This is for mobile/tablet popup
          allProducts={getAllAllProducts()}
          subcategoryContext={subcategoryContext}
        />
      )}
    </>
  )
}

export default SellzioPage
